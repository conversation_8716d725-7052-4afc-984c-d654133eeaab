// ==UserScript==
// @name         Wplace Auto-Complete
// @namespace    https://github.com/fabiojcp/
// @version      0.0.1
// @description  A userscript to automate and/or enhance the user experience on Wplace.live. Make sure to comply with the site's Terms of Service, and rules! This script is not affiliated with Wplace.live in any way, use at your own risk. This script is not affiliated with TamperMonkey. The author of this userscript is not responsible for any damages, issues, loss of data, or punishment that may occur as a result of using this script. This script is provided "as is" under the MPL-2.0 license. The "Blue Marble" icon is licensed under CC0 1.0 Universal (CC0 1.0) Public Domain Dedication. The image is owned by NASA.
// <AUTHOR>
// @license      MPL-2.0
// @supportURL   https://discord.gg/tpeBPy46hf
// @homepageURL  https://github.com/fabiojcp/Wplace-BlueMarble
// @icon         https://raw.githubusercontent.com/fabiojcp/Wplace-BlueMarble/794f1ed8ee3da7249a1dc31dfc312382999aab6b/dist/assets/Favicon.png
// @updateURL    https://raw.githubusercontent.com/fabiojcp/Wplace-BlueMarble/main/dist/BlueMarble.user.js
// @downloadURL  https://raw.githubusercontent.com/fabiojcp/Wplace-BlueMarble/main/dist/BlueMarble.user.js
// @run-at       document-start
// @match        *://*.wplace.live/*
// @grant        GM_getResourceText
// @grant        GM_addStyle
// @grant        GM.setValue
// @grant        GM_getValue
// @resource     CSS-BM-File https://raw.githubusercontent.com/SwingTheVine/Wplace-BlueMarble/794f1ed8ee3da7249a1dc31dfc312382999aab6b/dist/BlueMarble.user.css
// ==/UserScript==

// Wplace  --> https://wplace.live
// License --> https://www.mozilla.org/en-US/MPL/2.0/

(() => {
  var t,
    e,
    n = (t) => {
      throw TypeError(t);
    },
    i = (t, e, i) =>
      e.has(t)
        ? n('Cannot add the same private member more than once')
        : e instanceof WeakSet
        ? e.add(t)
        : e.set(t, i),
    s = (t, e, i) => (
      ((t, e) => {
        e.has(t) || n('Cannot access private method');
      })(t, e),
      i
    );
  function o(t, e) {
    if (0 === t) return e[0];
    let n = '';
    const i = e.length;
    for (; t > 0; ) (n = e[t % i] + n), (t = Math.floor(t / i));
    return n;
  }
  function a(t) {
    let e = '';
    for (let n = 0; n < t.length; n++) e += String.fromCharCode(t[n]);
    return btoa(e);
  }
  function r(t) {
    const e = atob(t),
      n = new Uint8Array(e.length);
    for (let t = 0; t < e.length; t++) n[t] = e.charCodeAt(t);
    return n;
  }
  (t = new WeakSet()),
    (e = function (t, e = {}, n = {}) {
      const i = document.createElement(t);
      this.t
        ? (this.i.appendChild(i), this.o.push(this.i), (this.i = i))
        : ((this.t = i), (this.i = i));
      for (const [t, n] of Object.entries(e)) i[t] = n;
      for (const [t, e] of Object.entries(n)) i[t] = e;
      return i;
    });
  var c,
    l,
    h,
    u = class {
      constructor({
        displayName: t = 'My template',
        l: e = 0,
        h: n = '',
        url: i = '',
        file: s = null,
        coords: o = null,
        u: a = null,
        m: r = 1e3,
      } = {}) {
        (this.displayName = t),
          (this.l = e),
          (this.h = n),
          (this.url = i),
          (this.file = s),
          (this.coords = o),
          (this.u = a),
          (this.m = r),
          (this.p = 0);
      }
      async v() {
        const t = await createImageBitmap(this.file),
          e = t.width,
          n = t.height,
          i = e * n;
        this.p = i;
        const s = {},
          o = {},
          r = new OffscreenCanvas(this.m, this.m),
          c = r.getContext('2d', { M: !0 });
        for (let i = this.coords[3]; i < n + this.coords[3]; ) {
          const l = Math.min(this.m - (i % this.m), n - (i - this.coords[3]));
          for (let n = this.coords[2]; n < e + this.coords[2]; ) {
            const h = Math.min(this.m - (n % this.m), e - (n - this.coords[2])),
              u = 3 * h,
              m = 3 * l;
            (r.width = u),
              (r.height = m),
              (c.imageSmoothingEnabled = !1),
              c.clearRect(0, 0, u, m),
              c.drawImage(
                t,
                n - this.coords[2],
                i - this.coords[3],
                h,
                l,
                0,
                0,
                3 * h,
                3 * l
              );
            const d = c.getImageData(0, 0, u, m);
            for (let t = 0; t < m; t++)
              for (let e = 0; e < u; e++)
                if (e % 3 != 1 || t % 3 != 1) {
                  const n = 4 * (t * u + e);
                  d.data[n + 3] = 0;
                }
            c.putImageData(d, 0, 0);
            const p = `${(this.coords[0] + Math.floor(n / 1e3))
              .toString()
              .padStart(4, '0')},${(this.coords[1] + Math.floor(i / 1e3))
              .toString()
              .padStart(4, '0')},${(n % 1e3).toString().padStart(3, '0')},${(
              i % 1e3
            )
              .toString()
              .padStart(3, '0')}`;
            s[p] = await createImageBitmap(r);
            const b = await r.convertToBlob(),
              f = await b.arrayBuffer(),
              w = Array.from(new Uint8Array(f));
            (o[p] = a(w)), (n += h);
          }
          i += l;
        }
        return { $: s, T: o };
      }
    };
  (c = new WeakSet()),
    (l = async function () {
      GM.setValue('bmTemplates', JSON.stringify(this.C));
    }),
    (h = async function (t) {
      const e = t.templates;
      if (Object.keys(e).length > 0)
        for (const t in e) {
          const n = t,
            i = e[t];
          if (e.hasOwnProperty(t)) {
            const t = n.split(' '),
              e = Number(t?.[0]),
              s = t?.[1] || '0',
              o = i.name || `Template ${e || ''}`,
              a = i.tiles,
              c = {};
            for (const t in a)
              if (a.hasOwnProperty(t)) {
                const e = r(a[t]),
                  n = new Blob([e], { type: 'image/png' }),
                  i = await createImageBitmap(n);
                c[t] = i;
              }
            const l = new u({
              displayName: o,
              l: e || this.D?.length || 0,
              h: s || '',
            });
            (l.u = c), this.D.push(l);
          }
        }
    });
  var m = GM_info.script.name.toString(),
    d = GM_info.script.version.toString();
  !(function (t) {
    const e = document.createElement('script');
    e.setAttribute('bm-o', m),
      e.setAttribute('bm-m', 'color: cornflowerblue;'),
      (e.textContent = `(${t})();`),
      document.documentElement.appendChild(e),
      e.remove();
  })(() => {
    const t = document.currentScript,
      e = t?.getAttribute('bm-o') || 'Blue Marble',
      n = t?.getAttribute('bm-m') || '',
      i = new Map();
    window.addEventListener('message', (t) => {
      const {
        source: s,
        endpoint: o,
        blobID: a,
        blobData: r,
        blink: c,
      } = t.data;
      if ((Date.now(), 'blue-marble' == s && a && r && !o)) {
        const t = i.get(a);
        'function' == typeof t
          ? t(r)
          : (function (...t) {
              (0, console.warn)(...t);
            })(
              `%c${e}%c: Attempted to retrieve a blob (%s) from queue, but the blobID was not a function! Skipping...`,
              n,
              '',
              a
            ),
          i.delete(a);
      }
    });
    const s = window.fetch;
    window.fetch = async function (...t) {
      const e = await s.apply(this, t),
        n = e.clone(),
        o = (t[0] instanceof Request ? t[0]?.url : t[0]) || 'ignore',
        a = n.headers.get('content-type') || '';
      if (a.includes('application/json'))
        n.json()
          .then((t) => {
            window.postMessage(
              { source: 'blue-marble', endpoint: o, jsonData: t },
              '*'
            );
          })
          .catch((t) => {});
      else if (a.includes('image/') && !o.includes('openfreemap')) {
        const t = Date.now(),
          e = await n.blob();
        return new Promise((s) => {
          const a = crypto.randomUUID();
          i.set(a, (t) => {
            s(
              new Response(t, {
                headers: n.headers,
                status: n.status,
                statusText: n.statusText,
              })
            );
          }),
            window.postMessage({
              source: 'blue-marble',
              endpoint: o,
              blobID: a,
              blobData: e,
              blink: t,
            });
        }).catch((t) => {
          Date.now();
        });
      }
      return e;
    };
  });
  var p = GM_getResourceText('CSS-BM-File');
  GM_addStyle(p);
  var b = document.createElement('link');
  (b.href =
    'https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100..700;1,100..700&display=swap'),
    (b.rel = 'preload'),
    (b.as = 'style'),
    (b.onload = function () {
      (this.onload = null), (this.rel = 'stylesheet');
    }),
    document.head.appendChild(b),
    new (class {
      constructor() {
        (this.I = null), (this.k = null), (this.N = '#bm-5');
      }
      S(t) {
        return (
          (this.k = t),
          (this.I = new MutationObserver((t) => {
            for (const e of t)
              for (const t of e.addedNodes)
                t instanceof HTMLElement && t.matches?.(this.N);
          })),
          this
        );
      }
      B() {
        return this.I;
      }
      observe(t, e = !1, n = !1) {
        t.observe(this.k, { childList: e, subtree: n });
      }
    })();
  var f = new (class {
      constructor(e, n) {
        i(this, t),
          (this.name = e),
          (this.version = n),
          (this.O = null),
          (this.L = 'bm-a'),
          (this.t = null),
          (this.i = null),
          (this.o = []);
      }
      H(t) {
        this.O = t;
      }
      j() {
        return this.o.length > 0 && (this.i = this.o.pop()), this;
      }
      q(t) {
        t.appendChild(this.t), (this.t = null), (this.i = null), (this.o = []);
      }
      A(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'div', {}, n)), this;
      }
      _(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'p', {}, n)), this;
      }
      F(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'small', {}, n)), this;
      }
      P(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'img', {}, n)), this;
      }
      R(n, i = {}, o = () => {}) {
        return o(this, s(this, t, e).call(this, 'h' + n, {}, i)), this;
      }
      G(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'hr', {}, n)), this;
      }
      U(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'br', {}, n)), this;
      }
      V(n = {}, i = () => {}) {
        const o = s(this, t, e).call(this, 'label', {
          textContent: n.textContent ?? '',
        });
        delete n.textContent;
        const a = s(this, t, e).call(this, 'input', { type: 'checkbox' }, n);
        return o.insertBefore(a, o.firstChild), this.j(), i(this, o, a), this;
      }
      Y(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'button', {}, n)), this;
      }
      J(n = {}, i = () => {}) {
        const o = n.title ?? n.textContent ?? 'Help: No info';
        delete n.textContent, (n.title = `Help: ${o}`);
        const a = {
          textContent: '?',
          className: 'bm-p',
          onclick: () => {
            this.W(this.L, o);
          },
        };
        return i(this, s(this, t, e).call(this, 'button', a, n)), this;
      }
      X(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'input', {}, n)), this;
      }
      Z(n = {}, i = () => {}) {
        const o = n.textContent ?? '';
        delete n.textContent;
        const a = s(this, t, e).call(this, 'div'),
          r = s(this, t, e).call(
            this,
            'input',
            {
              type: 'file',
              style:
                'display: none !important; visibility: hidden !important; position: absolute !important; left: -9999px !important; width: 0 !important; height: 0 !important; opacity: 0 !important;',
            },
            n
          );
        this.j();
        const c = s(this, t, e).call(this, 'button', { textContent: o });
        return (
          this.j(),
          this.j(),
          r.setAttribute('tabindex', '-1'),
          r.setAttribute('aria-hidden', 'true'),
          c.addEventListener('click', () => {
            r.click();
          }),
          r.addEventListener('change', () => {
            (c.style.maxWidth = `${c.offsetWidth}px`),
              r.files.length > 0
                ? (c.textContent = r.files[0].name)
                : (c.textContent = o);
          }),
          i(this, a, r, c),
          this
        );
      }
      K(n = {}, i = () => {}) {
        return i(this, s(this, t, e).call(this, 'textarea', {}, n)), this;
      }
      W(t, e, n = !1) {
        const i = document.getElementById(t.replace(/^#/, ''));
        i &&
          (i instanceof HTMLInputElement
            ? (i.value = e)
            : n
            ? (i.textContent = e)
            : (i.innerHTML = e));
      }
      tt(t, e) {
        let n,
          i = !1,
          s = 0,
          o = null,
          a = 0,
          r = 0,
          c = 0,
          l = 0;
        if (
          ((t = document.querySelector('#' == t?.[0] ? t : '#' + t)),
          (e = document.querySelector('#' == e?.[0] ? e : '#' + e)),
          !t || !e)
        )
          return void this.et(
            `Can not drag! ${t ? '' : 'moveMe'} ${t || e ? '' : 'and '}${
              e ? '' : 'iMoveThings '
            }was not found!`
          );
        const h = () => {
          if (i) {
            const e = Math.abs(a - c),
              n = Math.abs(r - l);
            (e > 0.5 || n > 0.5) &&
              ((a = c),
              (r = l),
              (t.style.transform = `translate(${a}px, ${r}px)`),
              (t.style.left = '0px'),
              (t.style.top = '0px'),
              (t.style.right = '')),
              (o = requestAnimationFrame(h));
          }
        };
        let u = null;
        const m = (m, d) => {
            (i = !0),
              (u = t.getBoundingClientRect()),
              (n = m - u.left),
              (s = d - u.top);
            const p = window.getComputedStyle(t).transform;
            if (p && 'none' !== p) {
              const t = new DOMMatrix(p);
              (a = t.m41), (r = t.m42);
            } else (a = u.left), (r = u.top);
            (c = a),
              (l = r),
              (document.body.style.userSelect = 'none'),
              e.classList.add('dragging'),
              o && cancelAnimationFrame(o),
              h();
          },
          d = () => {
            (i = !1),
              o && (cancelAnimationFrame(o), (o = null)),
              (document.body.style.userSelect = ''),
              e.classList.remove('dragging');
          };
        e.addEventListener('mousedown', function (t) {
          t.preventDefault(), m(t.clientX, t.clientY);
        }),
          e.addEventListener(
            'touchstart',
            function (t) {
              const e = t?.touches?.[0];
              e && (m(e.clientX, e.clientY), t.preventDefault());
            },
            { passive: !1 }
          ),
          document.addEventListener(
            'mousemove',
            function (t) {
              i && u && ((c = t.clientX - n), (l = t.clientY - s));
            },
            { passive: !0 }
          ),
          document.addEventListener(
            'touchmove',
            function (t) {
              if (i && u) {
                const e = t?.touches?.[0];
                if (!e) return;
                (c = e.clientX - n), (l = e.clientY - s), t.preventDefault();
              }
            },
            { passive: !1 }
          ),
          document.addEventListener('mouseup', d),
          document.addEventListener('touchend', d),
          document.addEventListener('touchcancel', d);
      }
      nt(t) {
        (0, console.info)(`${this.name}: ${t}`),
          this.W(this.L, 'Status: ' + t, !0);
      }
      et(t) {
        (0, console.error)(`${this.name}: ${t}`),
          this.W(this.L, 'Error: ' + t, !0);
      }
    })(m, d),
    w = new (class {
      constructor(t, e, n) {
        i(this, c),
          (this.name = t),
          (this.version = e),
          (this.t = n),
          (this.it = '1.0.0'),
          (this.st = null),
          (this.ot =
            "!#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[]^_`abcdefghijklmnopqrstuvwxyz{|}~"),
          (this.m = 1e3),
          (this.rt = 3),
          (this.ct = null),
          (this.lt = null),
          (this.ht = 'bm-n'),
          (this.ut = 'div#map canvas.maplibregl-canvas'),
          (this.dt = null),
          (this.bt = ''),
          (this.D = []),
          (this.C = null),
          (this.autoPaintEnabled = false),
          (this.autoPaintInterval = null),
          (this.paintDelay = 3000),
          (this.currentPixelIndex = 0),
          (this.pixelQueue = []);
      }
      ft() {
        if (document.body.contains(this.ct)) return this.ct;
        document.getElementById(this.ht)?.remove();
        const t = document.querySelector(this.ut),
          e = document.createElement('canvas');
        return (
          (e.id = this.ht),
          (e.className = 'maplibregl-canvas'),
          (e.style.position = 'absolute'),
          (e.style.top = '0'),
          (e.style.left = '0'),
          (e.style.height =
            t?.clientHeight * (window.devicePixelRatio || 1) + 'px'),
          (e.style.width =
            t?.clientWidth * (window.devicePixelRatio || 1) + 'px'),
          (e.height = t?.clientHeight * (window.devicePixelRatio || 1)),
          (e.width = t?.clientWidth * (window.devicePixelRatio || 1)),
          (e.style.zIndex = '8999'),
          (e.style.pointerEvents = 'none'),
          t?.parentElement?.appendChild(e),
          (this.ct = e),
          window.addEventListener('move', this.wt),
          window.addEventListener('zoom', this.vt),
          window.addEventListener('resize', this.yt),
          this.ct
        );
      }
      async xt() {
        return {
          whoami: this.name.replace(' ', ''),
          scriptVersion: this.version,
          schemaVersion: this.it,
          templates: {},
        };
      }
      async gt(t, e, n) {
        this.C || (this.C = await this.xt()),
          this.t.nt(`Creating template at ${n.join(', ')}...`);
        const i = new u({
            displayName: e,
            l: 0,
            h: o(this.st || 0, this.ot),
            file: t,
            coords: n,
          }),
          { $: a, T: r } = await i.v(this.m);
        (i.u = a),
          (this.C.templates[`${i.l} ${i.h}`] = {
            name: i.displayName,
            coords: n.join(', '),
            enabled: !0,
            tiles: r,
          }),
          (this.D = []),
          this.D.push(i);
        const h = new Intl.NumberFormat().format(i.p);
        this.t.nt(`Template created at ${n.join(', ')}! Total pixels: ${h}`),
          await s(this, c, l).call(this);
      }
      Mt() {}
      async $t() {
        this.C || (this.C = await this.xt());
      }
      async Tt(t, e) {
        const n = this.m * this.rt;
        e =
          e[0].toString().padStart(4, '0') +
          ',' +
          e[1].toString().padStart(4, '0');
        const i = this.D;
        i.sort((t, e) => t.l - e.l);
        const s = i
          .map((t) => {
            const n = Object.keys(t.u).filter((t) => t.startsWith(e));
            if (0 === n.length) return null;
            const i = n.map((e) => {
              const n = e.split(',');
              return { Ct: t.u[e], Dt: [n[0], n[1]], It: [n[2], n[3]] };
            });
            return i?.[0];
          })
          .filter(Boolean);
        if (s?.Ct?.length > 0) {
          const t = i
              .filter(
                (t) =>
                  Object.keys(t.u).filter((t) => t.startsWith(e)).length > 0
              )
              .reduce((t, e) => t + (e.p || 0), 0),
            n = new Intl.NumberFormat().format(t);
          this.t.nt(
            `Displaying ${s.Ct.length} template${
              1 == s.Ct.length ? '' : 's'
            }. Total pixels: ${n}`
          );
        }
        const o = await createImageBitmap(t),
          a = new OffscreenCanvas(n, n),
          r = a.getContext('2d');
        (r.imageSmoothingEnabled = !1),
          r.beginPath(),
          r.rect(0, 0, n, n),
          r.clip(),
          r.clearRect(0, 0, n, n),
          r.drawImage(o, 0, 0, n, n);
        for (const t of s)
          r.drawImage(
            t.Ct,
            Number(t.It[0]) * this.rt,
            Number(t.It[1]) * this.rt
          );
        return await a.convertToBlob({ type: 'image/png' });
      }
      kt(t) {
        'BlueMarble' == t?.whoami && s(this, c, h).call(this, t);
      }

      // Método para extrair pixels do template ordenados de baixo para cima, direita para esquerda
      extractPixelsFromTemplate() {
        if (!this.D || this.D.length === 0) {
          this.t.et('No template loaded!');
          return [];
        }

        const template = this.D[0];
        if (!template.file || !template.coords) {
          this.t.et('Template data is incomplete!');
          return [];
        }

        const pixels = [];
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Criar uma imagem do template
        const img = new Image();
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);

          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;

          // Extrair pixels de baixo para cima, direita para esquerda
          for (let y = canvas.height - 1; y >= 0; y--) {
            for (let x = canvas.width - 1; x >= 0; x--) {
              const index = (y * canvas.width + x) * 4;
              const r = data[index];
              const g = data[index + 1];
              const b = data[index + 2];
              const a = data[index + 3];

              // Só adicionar pixels não transparentes
              if (a > 0) {
                pixels.push({
                  x: template.coords[2] + x,
                  y: template.coords[3] + y,
                  color: this.rgbToWplaceColor(r, g, b),
                });
              }
            }
          }

          this.pixelQueue = pixels;
          this.t.nt(`Extracted ${pixels.length} pixels from template`);
        };

        // Converter file para URL se necessário
        if (template.file instanceof File) {
          const reader = new FileReader();
          reader.onload = (e) => {
            img.src = e.target.result;
          };
          reader.readAsDataURL(template.file);
        }

        return pixels;
      }

      // Converter RGB para cor do Wplace (aproximação)
      rgbToWplaceColor(r, g, b) {
        // Paleta básica do Wplace (pode precisar ser ajustada)
        const colors = [
          { r: 255, g: 255, b: 255, id: 0 }, // Branco
          { r: 0, g: 0, b: 0, id: 1 }, // Preto
          { r: 255, g: 0, b: 0, id: 2 }, // Vermelho
          { r: 0, g: 255, b: 0, id: 3 }, // Verde
          { r: 0, g: 0, b: 255, id: 4 }, // Azul
          { r: 255, g: 255, b: 0, id: 5 }, // Amarelo
          { r: 255, g: 0, b: 255, id: 6 }, // Magenta
          { r: 0, g: 255, b: 255, id: 7 }, // Ciano
        ];

        let closestColor = colors[0];
        let minDistance = Infinity;

        for (const color of colors) {
          const distance = Math.sqrt(
            Math.pow(r - color.r, 2) +
              Math.pow(g - color.g, 2) +
              Math.pow(b - color.b, 2)
          );

          if (distance < minDistance) {
            minDistance = distance;
            closestColor = color;
          }
        }

        return closestColor.id;
      }

      // Iniciar pintura automática
      startAutoPaint() {
        if (this.autoPaintEnabled) {
          this.t.et('Auto paint is already running!');
          return;
        }

        if (this.pixelQueue.length === 0) {
          this.extractPixelsFromTemplate();
          if (this.pixelQueue.length === 0) {
            this.t.et('No pixels to paint!');
            return;
          }
        }

        this.autoPaintEnabled = true;
        this.currentPixelIndex = 0;
        this.t.nt('Auto paint started!');

        this.autoPaintInterval = setInterval(() => {
          this.paintNextPixel();
        }, this.paintDelay);
      }

      // Parar pintura automática
      stopAutoPaint() {
        if (!this.autoPaintEnabled) {
          this.t.et('Auto paint is not running!');
          return;
        }

        this.autoPaintEnabled = false;
        if (this.autoPaintInterval) {
          clearInterval(this.autoPaintInterval);
          this.autoPaintInterval = null;
        }

        this.t.nt('Auto paint stopped!');
      }

      // Pintar próximo pixel
      paintNextPixel() {
        if (
          !this.autoPaintEnabled ||
          this.currentPixelIndex >= this.pixelQueue.length
        ) {
          this.stopAutoPaint();
          this.t.nt('Auto paint completed!');
          return;
        }

        const pixel = this.pixelQueue[this.currentPixelIndex];
        this.paintPixel(pixel.x, pixel.y, pixel.color);
        this.currentPixelIndex++;

        const progress = Math.round(
          (this.currentPixelIndex / this.pixelQueue.length) * 100
        );
        this.t.nt(
          `Auto painting... ${progress}% (${this.currentPixelIndex}/${this.pixelQueue.length})`
        );
      }

      // Pintar um pixel específico
      paintPixel(x, y, colorId) {
        try {
          // Simular clique no canvas na posição especificada
          const canvas = document.querySelector(this.ut);
          if (!canvas) {
            this.t.et('Canvas not found!');
            return;
          }

          // Calcular posição no canvas
          const rect = canvas.getBoundingClientRect();
          const canvasX = (x / 4000) * rect.width;
          const canvasY = (y / 4000) * rect.height;

          // Selecionar cor primeiro
          const colorButton = document.querySelector(`#color-${colorId}`);
          if (colorButton) {
            colorButton.click();
          }

          // Simular clique no canvas
          const clickEvent = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
            clientX: rect.left + canvasX,
            clientY: rect.top + canvasY,
          });

          canvas.dispatchEvent(clickEvent);
        } catch (error) {
          this.t.et(`Error painting pixel at (${x}, ${y}): ${error.message}`);
        }
      }
    })(m, d, f),
    v = new (class {
      constructor(t) {
        (this.Nt = t), (this.St = !1), (this.Bt = []), (this.Ot = []);
      }
      Lt(t) {
        window.addEventListener('message', async (e) => {
          const n = e.data,
            i = n.jsonData;
          if (!n || 'blue-marble' !== n.source) return;
          if (!n.endpoint) return;
          const s = n.endpoint
            ?.split('?')[0]
            .split('/')
            .filter((t) => t && isNaN(Number(t)))
            .filter((t) => t && !t.includes('.'))
            .pop();
          switch (s) {
            case 'me':
              if (i.status && '2' != i.status?.toString()[0])
                return void t.et(
                  'You are not logged in!\nCould not fetch userdata.'
                );
              const e = Math.ceil(
                Math.pow(Math.floor(i.level) * Math.pow(30, 0.65), 1 / 0.65) -
                  i.pixelsPainted
              );
              i.id || i.id,
                (this.Nt.st = i.id),
                t.W(
                  'bm-f',
                  `Username: <b>${(function (t) {
                    const e = document.createElement('div');
                    return (e.textContent = t), e.innerHTML;
                  })(i.name)}</b>`
                ),
                t.W(
                  'bm-b',
                  `Droplets: <b>${new Intl.NumberFormat().format(
                    i.droplets
                  )}</b>`
                ),
                t.W(
                  'bm-6',
                  `Next level in <b>${new Intl.NumberFormat().format(
                    e
                  )}</b> pixel${1 == e ? '' : 's'}`
                );
              break;
            case 'pixel':
              const s = n.endpoint
                  .split('?')[0]
                  .split('/')
                  .filter((t) => t && !isNaN(Number(t))),
                r = new URLSearchParams(n.endpoint.split('?')[1]),
                c = [r.get('x'), r.get('y')];
              if (this.Bt.length && (!s.length || !c.length))
                return void t.et(
                  'Coordinates are malformed!\nDid you try clicking the canvas first?'
                );
              this.Bt = [...s, ...c];
              const l =
                  ((o = s),
                  (a = c),
                  [
                    (parseInt(o[0]) % 4) * 1e3 + parseInt(a[0]),
                    (parseInt(o[1]) % 4) * 1e3 + parseInt(a[1]),
                  ]),
                h = document.querySelectorAll('span');
              for (const t of h)
                if (t.textContent.trim().includes(`${l[0]}, ${l[1]}`)) {
                  let e = document.querySelector('#bm-5');
                  const n = `(Tl X: ${s[0]}, Tl Y: ${s[1]}, Px X: ${c[0]}, Px Y: ${c[1]})`;
                  e
                    ? (e.textContent = n)
                    : ((e = document.createElement('span')),
                      (e.id = 'bm-5'),
                      (e.textContent = n),
                      (e.style =
                        'margin-left: calc(var(--spacing)*3); font-size: small;'),
                      t.parentNode.parentNode.parentNode.insertAdjacentElement(
                        'afterend',
                        e
                      ));
                }
              break;
            case 'tiles':
              let u = n.endpoint.split('/');
              u = [
                parseInt(u[u.length - 2]),
                parseInt(u[u.length - 1].replace('.png', '')),
              ];
              const m = n.blobID,
                d = n.blobData,
                p = await this.Nt.Tt(d, u);
              window.postMessage({
                source: 'blue-marble',
                blobID: m,
                blobData: p,
                blink: n.blink,
              });
              break;
            case 'robots':
              this.St = 'false' == i.userscript?.toString().toLowerCase();
          }
          var o, a;
        });
      }
    })(w);
  f.H(v);
  var y = JSON.parse(GM_getValue('bmTemplates', '{}'));
  w.kt(y),
    (function () {
      let t = !1;
      f.A({ id: 'bm-l', style: 'top: 10px; right: 75px;' })
        .A({ id: 'bm-7' })
        .A({ id: 'bm-g' })
        .j()
        .P(
          {
            alt: 'Blue Marble Icon - Click to minimize/maximize',
            src: 'https://raw.githubusercontent.com/SwingTheVine/Wplace-BlueMarble/main/dist/assets/Favicon.png',
            style: 'cursor: pointer;',
          },
          (e, n) => {
            n.addEventListener('click', () => {
              t = !t;
              const i = document.querySelector('#bm-l'),
                s = document.querySelector('#bm-7'),
                o = document.querySelector('#bm-g'),
                a = document.querySelector('#bm-8'),
                r = document.querySelector('#bm-c'),
                c = document.querySelector('#bm-d'),
                l = document.querySelectorAll('#bm-8 input');
              t ||
                ((i.style.width = 'auto'),
                (i.style.maxWidth = '300px'),
                (i.style.minWidth = '200px'),
                (i.style.padding = '10px')),
                [
                  '#bm-l h1',
                  '#bm-4',
                  '#bm-l hr',
                  '#bm-3 > *:not(#bm-8)',
                  '#bm-2',
                  '#bm-1',
                  `#${e.L}`,
                ].forEach((e) => {
                  document.querySelectorAll(e).forEach((e) => {
                    e.style.display = t ? 'none' : '';
                  });
                }),
                t
                  ? (a && (a.style.display = 'none'),
                    r && (r.style.display = 'none'),
                    c && (c.style.display = 'none'),
                    l.forEach((t) => {
                      t.style.display = 'none';
                    }),
                    (i.style.width = '60px'),
                    (i.style.height = '76px'),
                    (i.style.maxWidth = '60px'),
                    (i.style.minWidth = '60px'),
                    (i.style.padding = '8px'),
                    (n.style.marginLeft = '3px'),
                    (s.style.textAlign = 'center'),
                    (s.style.margin = '0'),
                    (s.style.marginBottom = '0'),
                    o &&
                      ((o.style.display = ''),
                      (o.style.marginBottom = '0.25em')))
                  : (a &&
                      ((a.style.display = ''),
                      (a.style.flexDirection = ''),
                      (a.style.justifyContent = ''),
                      (a.style.alignItems = ''),
                      (a.style.gap = ''),
                      (a.style.textAlign = ''),
                      (a.style.margin = '')),
                    r && (r.style.display = ''),
                    c && ((c.style.display = ''), (c.style.marginTop = '')),
                    l.forEach((t) => {
                      t.style.display = '';
                    }),
                    (n.style.marginLeft = ''),
                    (i.style.padding = '10px'),
                    (s.style.textAlign = ''),
                    (s.style.margin = ''),
                    (s.style.marginBottom = ''),
                    o && (o.style.marginBottom = '0.5em'),
                    (i.style.width = ''),
                    (i.style.height = '')),
                (n.alt = t
                  ? 'Blue Marble Icon - Minimized (Click to maximize)'
                  : 'Blue Marble Icon - Maximized (Click to minimize)');
            });
          }
        )
        .j()
        .R(1, { textContent: m })
        .j()
        .j()
        .G()
        .j()
        .A({ id: 'bm-4' })
        ._({ id: 'bm-f', textContent: 'Username:' })
        .j()
        ._({ id: 'bm-b', textContent: 'Droplets:' })
        .j()
        ._({ id: 'bm-6', textContent: 'Next level in...' })
        .j()
        .j()
        .G()
        .j()
        .A({ id: 'bm-3' })
        .A({ id: 'bm-8' })
        .Y(
          {
            id: 'bm-c',
            className: 'bm-p',
            style: 'margin-top: 0;',
            innerHTML:
              '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 6"><circle cx="2" cy="2" r="2"></circle><path d="M2 6 L3.7 3 L0.3 3 Z"></path><circle cx="2" cy="2" r="0.7" fill="white"></circle></svg></svg>',
          },
          (t, e) => {
            e.onclick = () => {
              const e = t.O?.Bt;
              e?.[0]
                ? (t.W('bm-h', e?.[0] || ''),
                  t.W('bm-i', e?.[1] || ''),
                  t.W('bm-j', e?.[2] || ''),
                  t.W('bm-k', e?.[3] || ''))
                : t.et(
                    'Coordinates are malformed! Did you try clicking on the canvas first?'
                  );
            };
          }
        )
        .j()
        .X({
          type: 'number',
          id: 'bm-h',
          placeholder: 'Tl X',
          min: 0,
          max: 2047,
          step: 1,
          required: !0,
        })
        .j()
        .X({
          type: 'number',
          id: 'bm-i',
          placeholder: 'Tl Y',
          min: 0,
          max: 2047,
          step: 1,
          required: !0,
        })
        .j()
        .X({
          type: 'number',
          id: 'bm-j',
          placeholder: 'Px X',
          min: 0,
          max: 2047,
          step: 1,
          required: !0,
        })
        .j()
        .X({
          type: 'number',
          id: 'bm-k',
          placeholder: 'Px Y',
          min: 0,
          max: 2047,
          step: 1,
          required: !0,
        })
        .j()
        .j()
        .Z({
          id: 'bm-2',
          textContent: 'Upload Template',
          accept: 'image/png, image/jpeg, image/webp, image/bmp, image/gif',
        })
        .j()
        .A({ id: 'bm-0' })
        .Y({ id: 'bm-d', textContent: 'Enable' }, (t, e) => {
          e.onclick = () => {
            const e = document.querySelector('#bm-2'),
              n = document.querySelector('#bm-h');
            if (!n.checkValidity())
              return (
                n.reportValidity(),
                void t.et(
                  'Coordinates are malformed! Did you try clicking on the canvas first?'
                )
              );
            const i = document.querySelector('#bm-i');
            if (!i.checkValidity())
              return (
                i.reportValidity(),
                void t.et(
                  'Coordinates are malformed! Did you try clicking on the canvas first?'
                )
              );
            const s = document.querySelector('#bm-j');
            if (!s.checkValidity())
              return (
                s.reportValidity(),
                void t.et(
                  'Coordinates are malformed! Did you try clicking on the canvas first?'
                )
              );
            const o = document.querySelector('#bm-k');
            if (!o.checkValidity())
              return (
                o.reportValidity(),
                void t.et(
                  'Coordinates are malformed! Did you try clicking on the canvas first?'
                )
              );
            e?.files[0]
              ? (w.gt(e.files[0], e.files[0]?.name.replace(/\.[^/.]+$/, ''), [
                  Number(n.value),
                  Number(i.value),
                  Number(s.value),
                  Number(o.value),
                ]),
                t.nt('Drew to canvas!'))
              : t.et('No file selected!');
          };
        })
        .j()
        .A({ id: 'bm-auto-paint-controls', style: 'margin-top: 10px;' })
        .Y(
          {
            id: 'bm-start-paint',
            textContent: 'Start Auto Paint',
            className: 'bm-p',
            style: 'background-color: #4CAF50; margin-right: 5px;',
          },
          (t, e) => {
            e.onclick = () => {
              w.startAutoPaint();
            };
          }
        )
        .j()
        .Y(
          {
            id: 'bm-stop-paint',
            textContent: 'Stop Auto Paint',
            className: 'bm-p',
            style: 'background-color: #f44336;',
          },
          (t, e) => {
            e.onclick = () => {
              w.stopAutoPaint();
            };
          }
        )
        .j()
        .X(
          {
            type: 'number',
            id: 'bm-paint-delay',
            placeholder: 'Delay (ms)',
            min: 1000,
            max: 10000,
            step: 100,
            value: 3000,
            style: 'margin-top: 5px; width: 100%;',
          },
          (t, e) => {
            e.addEventListener('change', () => {
              w.paintDelay = parseInt(e.value) || 3000;
              t.nt(`Paint delay set to ${w.paintDelay}ms`);
            });
          }
        )
        .j()
        .j()
        .j()
        .j()
        .K({
          id: f.L,
          placeholder: `Status: Sleeping...\nVersion: ${d}`,
          readOnly: !0,
        })
        .j()
        .A({ id: 'bm-1' })
        .A()
        .Y(
          {
            id: 'bm-9',
            className: 'bm-p',
            innerHTML: '🎨',
            title: 'Template Color Converter',
          },
          (t, e) => {
            e.addEventListener('click', () => {
              window.open(
                'https://pepoafonso.github.io/color_converter_wplace/',
                '_blank',
                'noopener noreferrer'
              );
            });
          }
        )
        .j()
        .j()
        .F({ textContent: 'Made by SwingTheVine', style: 'margin-top: auto;' })
        .j()
        .j()
        .j()
        .q(document.body);
    })(),
    f.tt('#bm-l', '#bm-g'),
    v.Lt(f),
    new MutationObserver((t, e) => {
      const n = document.querySelector('#color-1');
      if (!n) return;
      let i = document.querySelector('#bm-e');
      i ||
        ((i = document.createElement('button')),
        (i.id = 'bm-e'),
        (i.textContent = 'Move ↑'),
        (i.className = 'btn btn-soft'),
        (i.onclick = function () {
          const t = this.parentNode.parentNode.parentNode.parentNode,
            e = 'Move ↑' == this.textContent;
          (t.parentNode.className = t.parentNode.className.replace(
            e ? 'bottom' : 'top',
            e ? 'top' : 'bottom'
          )),
            (t.style.borderTopLeftRadius = e ? '0px' : 'var(--radius-box)'),
            (t.style.borderTopRightRadius = e ? '0px' : 'var(--radius-box)'),
            (t.style.borderBottomLeftRadius = e ? 'var(--radius-box)' : '0px'),
            (t.style.borderBottomRightRadius = e ? 'var(--radius-box)' : '0px'),
            (this.textContent = e ? 'Move ↓' : 'Move ↑');
        }),
        n.parentNode.parentNode.parentNode.parentNode
          .querySelector('h2')
          .parentNode.appendChild(i));
    }).observe(document.body, { childList: !0, subtree: !0 }),
    (function (...t) {
      (0, console.log)(...t);
    })(`%c${m}%c (${d}) userscript has loaded!`, 'color: cornflowerblue;', '');
})();
